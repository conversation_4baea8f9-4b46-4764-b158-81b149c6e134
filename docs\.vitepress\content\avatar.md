# Avatar
An image element with a fallback for representing the user.

<script setup>
import { Avatar } from 'qh-ui'
</script>

<ComponentPreview name="AvatarDemo">
  <Avatar
    size="2xl"
    fallback="QH"
  />
</ComponentPreview>

## API Reference

### Props

| 属性 | 类型 | 默认值 | 描述 |
| --- | --- | --- | --- |
| `size` | `'3xs' \| '2xs' \| 'xs' \| 'sm' \| 'md' \| 'lg' \| 'xl' \| '2xl' \| '3xl'` | `'md'` | 头像大小 |
| `src` | `string` | `undefined` | 图片地址 |
| `alt` | `string` | `undefined` | 图片alt文本 |
| `fallback` | `string` | `undefined` | 加载失败时的占位符文本 |
| `radius` | `'full' \| 'lg' \| 'md' \| 'sm'` | `'md'` | 圆角大小 |

### Size 尺寸对照

| Size | 尺寸 | Tailwind 类 |
| --- | --- | --- |
| `3xs` | 16px | `w-4 h-4` |
| `2xs` | 20px | `w-5 h-5` |
| `xs` | 24px | `w-6 h-6` |
| `sm` | 28px | `w-7 h-7` |
| `md` | 32px | `w-8 h-8` |
| `lg` | 36px | `w-9 h-9` |
| `xl` | 40px | `w-10 h-10` |
| `2xl` | 44px | `w-11 h-11` |
| `3xl` | 48px | `w-12 h-12` |

### Events

Avatar 组件继承自 `reka-ui` 的 `AvatarImage` 组件，支持以下事件：

| 事件名 | 类型 | 描述 |
| --- | --- | --- |
| `loadingStatusChange` | `(status: 'idle' \| 'loading' \| 'loaded' \| 'error') => void` | 图片加载状态变化时触发 |

### CSS 类名

组件提供以下 CSS 类名用于自定义样式：

| 类名 | 描述 |
| --- | --- |
| `.qh-avatar-root` | 头像根容器 |
| `.qh-avatar-image` | 头像图片元素 |
| `.qh-avatar-fallback` | 头像占位符容器 |

### 使用示例

```vue
<template>
  <!-- 基础用法 -->
  <Avatar fallback="QH" />

  <!-- 带图片 -->
  <Avatar
    src="https://example.com/avatar.jpg"
    alt="用户头像"
    fallback="QH"
  />

  <!-- 不同尺寸 -->
  <Avatar size="sm" fallback="S" />
  <Avatar size="lg" fallback="L" />
  <Avatar size="2xl" fallback="XL" />

  <!-- 不同圆角 -->
  <Avatar radius="full" fallback="QH" />
  <Avatar radius="sm" fallback="QH" />
</template>
```
