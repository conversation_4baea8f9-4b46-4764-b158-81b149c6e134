{
  // tsconfig for type-check only
  "extends": "@vue/tsconfig/tsconfig.dom.json",
  "compilerOptions": {
    "target": "esnext",
    "jsx": "preserve",
    "lib": [
      "esnext",
      "dom"
    ],
    "baseUrl": ".",
    "module": "esnext",
    "moduleResolution": "node",
    "paths": {
      "@/*": [
        "src/*"
      ]
    },
    "resolveJsonModule": true,
    "strict": true,
    "declaration": false,
    "outDir": "dist",
    "sourceMap": true,
    "esModuleInterop": true,
    "skipLibCheck": true
  },
  "include": [
    "env.d.ts",
    "src/**/*",
    "src/**/*.ts",
    "src/**/*.tsx",
    "src/**/*.vue"
  ],
  "exclude": [
    "src/**/__tests__/*",
    "**/*.test.ts",
    "**/*.test.js",
    "src/**/*.story.vue",
    "src/**/story/*"
  ]
}
