{"editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "editor.defaultFormatter": "dbaeumer.vscode-eslint", "eslint.format.enable": true, "prettier.enable": false, "[typescript]": {"editor.defaultFormatter": "dbaeumer.vscode-eslint"}, "[javascript]": {"editor.defaultFormatter": "dbaeumer.vscode-eslint"}, "[vue]": {"editor.defaultFormatter": "dbaeumer.vscode-eslint"}}