import type { AvatarProps } from '@/Avatar'
import type { ButtonProps } from '@/Button'
import type { CheckboxProps } from '@/Checkbox'
import type { ComboBoxOption, ComboBoxProps } from '@/ComboBox'
import type { InputProps } from '@/Input'
import type { NumberFieldProps } from '@/NumberField'
import type { PinInputProps } from '@/PinInput'
import type { RadioGroupProps, RadioOption } from '@/RadioGroup'
import type { SelectItemOption, SelectOption, SelectProps } from '@/Select'
import type { SliderProps } from '@/Slider'
import type { SwitchProps } from '@/Switch'
// import type { IconProps } from '@/Icon'
import { Avatar } from '@/Avatar'
import { Button } from '@/Button'
import { Checkbox } from '@/Checkbox'
import { ComboBox } from '@/ComboBox'
import { Icon } from '@/Icon'
import { Input } from '@/Input'
import { NumberField } from '@/NumberField'
import { PinInput } from '@/PinInput'
import { RadioGroup } from '@/RadioGroup'
import { Select } from '@/Select'
import { Slider } from '@/Slider'
import { Switch } from '@/Switch'

export {
  Avatar,
  Button,
  Checkbox,
  ComboBox,
  Icon,
  Input,
  NumberField,
  PinInput,
  RadioGroup,
  Select,
  Slider,
  Switch,
  // ... other components
}

export type {
  AvatarProps,
  ButtonProps,
  CheckboxProps,
  ComboBoxOption,
  ComboBoxProps,
  InputProps,
  NumberFieldProps,
  PinInputProps,
  RadioGroupProps,
  RadioOption,
  SelectItemOption,
  SelectOption,
  SelectProps,
  SliderProps,
  SwitchProps,
  // IconProps,
}
