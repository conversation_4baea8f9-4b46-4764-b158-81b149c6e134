import type { MarkdownEnv, <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'vitepress'
import { existsSync, readFileSync } from 'node:fs'
import { dirname, resolve } from 'node:path'

export default function (md: Markdown<PERSON>enderer) {
  md.core.ruler.before('normalize', 'component-preview', (state) => {
    const insertComponentImport = (importString: string) => {
      if (!state.src.includes('<script setup>')) {
        state.src = `<script setup>\n${importString}\n</script>\n\n${state.src}`
      }
      else {
        state.src = state.src.replace('</script>', `${importString}\n</script>`)
      }
    }

    const processComponentPreview = (name: string) => {
      const pathName = `./.vitepress/demo/${name}`
      insertComponentImport(`import ${name} from '${pathName}.vue'`)

      const { realPath, path: _path } = state.env as MarkdownEnv

      // Check if demo file exists
      const demoPath = resolve(dirname(realPath ?? _path), `${pathName}.vue`)
      const fileExists = existsSync(demoPath)

      if (!fileExists) {
        console.warn(`Demo file not found: ${demoPath}`)
        return null
      }

      const groupedFiles = { demo: [`${name}.vue`] }

      // 读取文件内容
      let fileContent = ''
      try {
        fileContent = readFileSync(demoPath, 'utf-8')
      }
      catch (error) {
        console.warn(`Failed to read demo file: ${demoPath}`, error)
        fileContent = '// Failed to load file content'
      }

      return {
        groupedFiles,
        fileContent,
      }
    }

    // 处理自闭合标签：<ComponentPreview name="xxx" />
    const selfClosingRegex = /<ComponentPreview\s+name="([^"]+)"\s*\/>/g
    state.src = state.src.replace(selfClosingRegex, (_, name) => {
      const result = processComponentPreview(name)
      if (!result) {
        return `<div class="error">Demo file not found: ${name}.vue</div>`
      }

      const { groupedFiles, fileContent } = result
      return `<ComponentPreview name="${name}" files="${encodeURIComponent(JSON.stringify(groupedFiles))}" >
<${name} />

<template #demo>

\`\`\`vue[${name}.vue]
${fileContent}
\`\`\`

</template>
</ComponentPreview>`
    })

    // 处理带内容的标签：<ComponentPreview name="xxx">...</ComponentPreview>
    const contentRegex = /<ComponentPreview\s+name="([^"]+)"([^>]*)>([\s\S]*?)<\/ComponentPreview>/g
    state.src = state.src.replace(contentRegex, (match, name, attributes, content) => {
      // 检查是否已经有 files 属性
      if (attributes.includes('files=')) {
        return match // 已经处理过，保持原样
      }

      const result = processComponentPreview(name)
      if (!result) {
        return `<div class="error">Demo file not found: ${name}.vue</div>`
      }

      const { groupedFiles, fileContent } = result

      // 检查内容中是否已经有 #demo 插槽
      const hasCodeSlot = content.includes('<template #demo>')

      if (hasCodeSlot) {
        // 如果已经有代码插槽，只添加 files 属性
        return `<ComponentPreview name="${name}" files="${encodeURIComponent(JSON.stringify(groupedFiles))}"${attributes}>${content}</ComponentPreview>`
      }
      else {
        // 如果没有代码插槽，添加默认的代码插槽
        return `<ComponentPreview name="${name}" files="${encodeURIComponent(JSON.stringify(groupedFiles))}"${attributes}>${content}

<template #demo>

\`\`\`vue[${name}.vue]
${fileContent}
\`\`\`

</template>
</ComponentPreview>`
      }
    })
  })
}
