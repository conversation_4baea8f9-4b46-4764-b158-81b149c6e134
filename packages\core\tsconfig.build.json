{
  // This tsconfig is for development. Allowing *.story.vue, and __test__/*.vue to have types support
  "extends": "@vue/tsconfig/tsconfig.dom.json",
  "compilerOptions": {
    "target": "esnext",
    "jsx": "preserve",
    "lib": ["esnext", "dom"],
    "baseUrl": ".",
    "module": "esnext",
    "moduleResolution": "node",
    "paths": {
      "@/*": ["src/*"],
      "@vue/shared": ["node_modules/@vue/shared"]
    },
    "resolveJsonModule": true,
    "strict": true,
    "declaration": false,
    "outDir": "dist",
    // "sourceMap": true,
    "esModuleInterop": true,
    "skipLibCheck": true
  },
  "include": [
    "env.d.ts",
    "constant/*",
    "src/**/*",
    "src/**/*.ts",
    "src/**/*.tsx",
    "src/**/*.vue"
  ],
  "exclude": [
    "src/**/*.test.ts"
  ]
}
