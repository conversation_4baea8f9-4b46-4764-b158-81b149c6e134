{"name": "qh-ui", "type": "module", "version": "0.0.1", "private": true, "workspaces": ["packages/*"], "packageManager": "pnpm@10.7.1", "license": "MIT", "scripts": {"playground:dev": "pnpm --filter playground-vue dev", "docs:dev": "pnpm --filter docs docs:dev", "docs:build": "pnpm --filter docs docs:build", "core:dev": "rimraf packages/core/dist && pnpm --filter qh-ui dev", "build-only": "rimraf packages/core/dist && pnpm run -r build-only", "lint": "eslint .", "lint:fix": "eslint . --fix", "bumpp": "bumpp package.json packages/*/package.json docs/package.json"}, "devDependencies": {"@antfu/eslint-config": "^4.16.2", "eslint": "^9.30.1", "lint-staged": "^16.1.2", "rimraf": "^6.0.1", "typescript": "^5.8.3"}, "pnpm": {"overrides": {"vue": "3.5.17"}}, "lint-staged": {"*": "eslint --fix"}}