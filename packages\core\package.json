{"name": "qh-ui", "type": "module", "version": "0.0.1", "description": "A UI library for Vue 3", "author": "<PERSON><PERSON>-M (https://github.com/<PERSON><PERSON>-<PERSON>)", "license": "MIT", "homepage": "https://github.com/As<PERSON>-M/qh-ui", "repository": {"type": "git", "url": "git+https://github.com/Asaki-M/qh-ui.git"}, "bugs": {"url": "https://github.com/As<PERSON>-M/qh-ui/issues"}, "keywords": ["ui", "vue", "headless", "components", "radix"], "sideEffects": false, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.cjs"}, "./index.css": "./dist/index.css"}, "main": "./dist/index.cjs", "module": "./dist/index.js", "types": "./dist/index.d.ts", "typings": "./dist/index.d.ts", "files": ["./LICENSE", "./dist"], "scripts": {"build": "pnpm type-check && pnpm build-only", "build-only": "vite build", "dev": "vite build --watch", "type-check": "vue-tsc -p tsconfig.check.json --noEmit", "type-gen": "vue-tsc --declaration  --emitDeclarationOnly", "pub:release": "pnpm publish --no-git-checks --access public"}, "peerDependencies": {"vue": ">= 3.2.0"}, "dependencies": {"@vueuse/core": "^12.5.0", "reka-ui": "^2.3.2"}, "devDependencies": {"@iconify/vue": "^5.0.0", "@tailwindcss/vite": "^4.1.11", "@tsconfig/node18": "^18.2.4", "@types/node": "^20.17.17", "@vitejs/plugin-vue": "^5.2.4", "@vue/tsconfig": "^0.7.0", "tailwindcss": "^4.1.11", "vite": "^5.4.19", "vite-plugin-dts": "^4.5.4", "vue": "3.5.13", "vue-tsc": "2.2.10"}}